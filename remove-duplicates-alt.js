/**
 * Удаление дублей из custom ALT таблицы
 */

const fs = require('fs');

async function removeDuplicates() {
    console.log('🔥 УДАЛЕНИЕ ДУБЛЕЙ ИЗ CUSTOM ALT ТАБЛИЦЫ');
    console.log('=' .repeat(60));

    try {
        // 1. Загружаем файл
        const data = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
        console.log(`📁 Загружен файл: ${data.totalAccounts} адресов`);

        // 2. Проверяем дубли в custom таблице
        const customAddresses = data.tables.custom.addresses;
        console.log(`📊 Custom таблица: ${customAddresses.length} адресов`);

        const duplicates = customAddresses.filter((addr, index) => customAddresses.indexOf(addr) !== index);
        console.log(`🔍 Найдено дублей: ${duplicates.length}`);

        if (duplicates.length > 0) {
            console.log('🗑️ ДУБЛИ:');
            duplicates.forEach(addr => console.log(`   ${addr}`));
        }

        // 3. Убираем дубли
        const uniqueAddresses = [...new Set(customAddresses)];
        console.log(`✅ Уникальных адресов: ${uniqueAddresses.length}`);
        console.log(`🗑️ Удалено дублей: ${customAddresses.length - uniqueAddresses.length}`);

        // 4. Обновляем данные
        data.tables.custom.addresses = uniqueAddresses;
        data.tables.custom.accountCount = uniqueAddresses.length;
        data.totalAccounts = data.tables.marginfi1.accountCount + uniqueAddresses.length;
        data.timestamp = new Date().toISOString();
        data.source = "🗑️ УДАЛЕНЫ ДУБЛИ ИЗ CUSTOM ТАБЛИЦЫ";

        // 5. Создаем резервную копию
        const backupFile = `custom-alt-data.json.backup.${Date.now()}`;
        fs.writeFileSync(backupFile, fs.readFileSync('custom-alt-data.json', 'utf8'));
        console.log(`💾 Резервная копия: ${backupFile}`);

        // 6. Сохраняем очищенный файл
        fs.writeFileSync('custom-alt-data.json', JSON.stringify(data, null, 4));
        console.log('💾 Файл обновлен без дублей!');

        // 7. Проверяем результат
        const verifyData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
        console.log('\n🔍 ПРОВЕРКА РЕЗУЛЬТАТА:');
        console.log(`📊 Всего адресов: ${verifyData.totalAccounts}`);
        console.log(`📊 MarginFi адресов: ${verifyData.tables.marginfi1.accountCount}`);
        console.log(`📊 Custom адресов: ${verifyData.tables.custom.accountCount}`);

        // Проверяем наши BIN ARRAYS
        const ourBinArrays = [
            'CnwbYbAjLGiDiGNNDVp2jjHBm41gL2t9xfjU5iCvDtET',
            '3PtxzAh6LxUteSy97M9thBkZQHfzWbRLSi8G6oJjfynd'
        ];

        console.log('\n🔍 ПРОВЕРКА НАШИХ BIN ARRAYS:');
        ourBinArrays.forEach((addr, index) => {
            const found = verifyData.tables.custom.addresses.includes(addr);
            const count = verifyData.tables.custom.addresses.filter(a => a === addr).length;
            console.log(`   ${index + 1}. ${addr}: ${found ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'} (${count} раз)`);
        });

        console.log('\n🎉 ОЧИСТКА ЗАВЕРШЕНА!');
        console.log('✅ Дубли удалены');
        console.log('✅ Файл готов к использованию');
        console.log('✅ Компилятор теперь сможет сжать адреса');

    } catch (error) {
        console.error('❌ Ошибка:', error.message);
        process.exit(1);
    }
}

removeDuplicates();
