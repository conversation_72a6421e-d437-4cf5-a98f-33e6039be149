/**
 * Скачивание обновленной ALT таблицы с новыми BIN ARRAYS
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

async function downloadUpdatedALT() {
    console.log('🔥 СКАЧИВАНИЕ ОБНОВЛЕННОЙ ALT ТАБЛИЦЫ С НОВЫМИ BIN ARRAYS');
    console.log('=' .repeat(80));

    try {
        // Подключение к RPC
        const connection = new Connection(process.env.SOLANA_RPC_URL);
        console.log('✅ Подключение к RPC установлено');

        // Кастомная ALT таблица
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Скачиваем ALT таблицу: ${customALTAddress.toString()}`);

        // Получаем данные таблицы
        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        
        if (!altAccount.value) {
            throw new Error('ALT таблица не найдена!');
        }

        const addresses = altAccount.value.state.addresses;
        console.log(`📊 Найдено адресов в таблице: ${addresses.length}`);

        // Проверяем наши новые BIN ARRAYS
        const newBinArrays = [
            'CnwbYbAjLGiDiGNNDVp2jjHBm41gL2t9xfjU5iCvDtET',
            '3PtxzAh6LxUteSy97M9thBkZQHfzWbRLSi8G6oJjfynd'
        ];

        console.log('\n🔍 ПРОВЕРКА НОВЫХ BIN ARRAYS:');
        newBinArrays.forEach((addr, index) => {
            const found = addresses.some(a => a.toString() === addr);
            console.log(`   ${index + 1}. ${addr}: ${found ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'}`);
        });

        // Создаем структуру для сохранения
        const altData = {
            address: customALTAddress.toString(),
            addresses: addresses.map(addr => addr.toString()),
            timestamp: Date.now(),
            totalAddresses: addresses.length
        };

        // Сохраняем в файл
        const filename = 'custom-alt-data-updated.json';
        fs.writeFileSync(filename, JSON.stringify(altData, null, 2));
        console.log(`\n💾 Данные сохранены в: ${filename}`);

        // Показываем последние добавленные адреса
        console.log('\n📋 ПОСЛЕДНИЕ 5 АДРЕСОВ В ТАБЛИЦЕ:');
        const lastAddresses = addresses.slice(-5);
        lastAddresses.forEach((addr, index) => {
            const globalIndex = addresses.length - 5 + index;
            console.log(`   [${globalIndex}] ${addr.toString()}`);
        });

        console.log('\n🎉 СКАЧИВАНИЕ ЗАВЕРШЕНО!');
        console.log(`📊 Всего адресов: ${addresses.length}`);
        console.log(`💾 Файл: ${filename}`);

    } catch (error) {
        console.error('❌ Ошибка скачивания ALT таблицы:', error.message);
        process.exit(1);
    }
}

downloadUpdatedALT();
