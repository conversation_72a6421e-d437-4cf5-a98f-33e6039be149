const { Connection, PublicKey } = require('@solana/web3.js');
const { BN } = require('@coral-xyz/anchor');
const DLMM = require('@meteora-ag/dlmm').default;

/**
 * 🎯 ЧИСТЫЙ METEORA BIN CACHE MANAGER
 * 
 * ТОЛЬКО НЕОБХОДИМЫЕ МЕТОДЫ:
 * 1. getArbitrageData() - получение цен через getBinsAroundActiveBin(1,1) - ТОЛЬКО 3 БИНА: АКТИВНЫЙ ± 1!
 * 2. createFastSwap() - создание swap транзакций через getBinArrayForSwap() + swap()
 * 3. Кэширование DLMM инстансов
 * 
 * УДАЛЕНО:
 * - Автообновление каждые 900ms
 * - Старые методы парсинга
 * - Дублирующие запросы
 * - Лишние setInterval
 */
class MeteoraBinCacheManager {
    constructor() {
        console.log('🚀 Meteora Active Bin Cache Manager инициализирован');

        // 💾 КЭШИ
        this.dlmmInstancesCache = new Map(); // Кэш DLMM инстансов
        this.binArraysCache = new Map(); // 🔥 КЭШ 3 БИНОВ ДЛЯ КАЖДОГО ПУЛА!
        
        // ⏱️ НАСТРОЙКИ КЭШИРОВАНИЯ
        this.ACTIVE_BIN_CACHE_DURATION = 60000; // 60 секунд

        // 🚀 ПРОДАКШЕН: КЭШ ДЛЯ PDA АДРЕСОВ (КЛЮЧ: poolAddress_chunkIndex)
        this.pdaCache = new Map();
        this.pdaCacheTimeout = 5000; // 5 секунд кеш для PDA

        // 🚀 ПРОДАКШЕН: КЭШ ДЛЯ ПРОВЕРКИ СУЩЕСТВОВАНИЯ АККАУНТОВ
        this.accountExistsCache = new Map();
        this.accountExistsCacheTimeout = 2000; // 2 секунды кеш для проверки существования

        // 🎯 РЕЖИМ АРБИТРАЖА (скрывает логи)
        this.arbitrageMode = false;
        
        console.log('⚡ Кэш активных бинов: 60 секунд (оптимизировано под RPC лимиты)');
    }

    /**
     * 🌐 ПОЛУЧЕНИЕ RPC ПОДКЛЮЧЕНИЯ
     */
    async getRPCForOperation(operation) {
        const { globalRPCManager } = require('./centralized-rpc-manager.js');
        
        if (operation === 'meteora') {
            // Для Meteora SDK используем Helius (QuickNode отключил методы)
            if (!this.arbitrageMode) {
                console.log('🔥 METEORA SDK ЗАПРОС → Helius (QuickNode отключил методы!)');
            }
            return globalRPCManager.getConnection('helius');
        }
        
        return globalRPCManager.getConnection('quicknode');
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ СТРОКОВОГО ПРЕДСТАВЛЕНИЯ АДРЕСА ПУЛА
     */
    getPoolStr(poolAddress) {
        if (!poolAddress) {
            throw new Error(`🚨 КРИТИЧЕСКАЯ ОШИБКА: poolAddress не передан в getPoolStr! Получен: ${poolAddress}`);
        }
        return typeof poolAddress === 'string' ? poolAddress : poolAddress.toString();
    }

    /**
     * 🚀 ПРОДАКШЕН: ПОЛУЧЕНИЕ PDA С КЕШИРОВАНИЕМ
     */
    getRequiredBinArrays(poolAddress, targetBinIndexes) {
        const poolStr = poolAddress.toString();

        // 🎯 ВЫЧИСЛЯЕМ УНИКАЛЬНЫЕ CHUNK INDEXES
        const chunkIndexes = [...new Set(targetBinIndexes.map(binId => Math.floor(binId / 64)))];
        const binArrayPDAs = [];

        for (const chunkIndex of chunkIndexes) {
            const cacheKey = `${poolStr}_${chunkIndex}`;
            const now = Date.now();

            // 🔥 ПРОВЕРЯЕМ КЭШ PDA
            const cached = this.pdaCache.get(cacheKey);
            if (cached && (now - cached.timestamp) < this.pdaCacheTimeout) {
                binArrayPDAs.push(cached.pda);
                continue;
            }

            // 🔥 ГЕНЕРИРУЕМ PDA С ПРАВИЛЬНЫМ ФОРМАТОМ
            const chunkBuffer = Buffer.alloc(8);
            chunkBuffer.writeBigInt64LE(BigInt(chunkIndex));

            const [pda] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('bin_array'),
                    new PublicKey(poolStr).toBuffer(),
                    chunkBuffer
                ],
                new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')
            );

            // 🔥 СОХРАНЯЕМ В КЭШ
            this.pdaCache.set(cacheKey, { pda, timestamp: now });
            binArrayPDAs.push(pda);
        }

        return { chunkIndexes, binArrayPDAs };
    }

    /**
     * 🚀 ПРОДАКШЕН: ПРОВЕРКА СУЩЕСТВОВАНИЯ АККАУНТОВ С КЕШИРОВАНИЕМ
     */
    async checkAccountsExist(connection, pdas) {
        const results = [];
        const uncachedPDAs = [];
        const uncachedIndexes = [];
        const now = Date.now();

        // 🔥 ПРОВЕРЯЕМ КЭШ СУЩЕСТВОВАНИЯ
        for (let i = 0; i < pdas.length; i++) {
            const pdaStr = pdas[i].toString();
            const cached = this.accountExistsCache.get(pdaStr);

            if (cached && (now - cached.timestamp) < this.accountExistsCacheTimeout) {
                results[i] = cached.exists;
            } else {
                uncachedPDAs.push(pdas[i]);
                uncachedIndexes.push(i);
            }
        }

        // 🔥 ПРОВЕРЯЕМ НЕКЕШИРОВАННЫЕ АККАУНТЫ
        if (uncachedPDAs.length > 0) {
            const accounts = await connection.getMultipleAccountsInfo(uncachedPDAs);

            for (let j = 0; j < accounts.length; j++) {
                const exists = accounts[j] !== null;
                const originalIndex = uncachedIndexes[j];
                const pdaStr = uncachedPDAs[j].toString();

                results[originalIndex] = exists;

                // 🔥 СОХРАНЯЕМ В КЭШ
                this.accountExistsCache.set(pdaStr, { exists, timestamp: now });
            }
        }

        return results;
    }

    /**
     * 🎯 ГЛАВНЫЙ МЕТОД: ПОЛУЧЕНИЕ ДАННЫХ ДЛЯ АРБИТРАЖА
     * Один RPC запрос на пул через getBinsAroundActiveBin(1,1) - ТОЛЬКО 3 БИНА: АКТИВНЫЙ ± 1!
     * Возвращает цены + готовые DLMM инстансы для создания транзакций
     */
    async getArbitrageData(poolAddresses) {
        const startTime = Date.now();

        try {
            if (!this.arbitrageMode) {
                console.log(`🎯 ПОЛУЧЕНИЕ ДАННЫХ ДЛЯ АРБИТРАЖА: ${poolAddresses.length} пулов`);
            }

            if (poolAddresses.length === 0) return [];

            // 🔥 ПОЛУЧАЕМ CONNECTION ЧЕРЕЗ ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР!
            const connection = await this.getRPCForOperation('meteora');
            console.log(`🔥 ИСПОЛЬЗУЕМ RPC ДЛЯ METEORA ОПЕРАЦИЙ`);

            // 🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ 3 БИНА ИЗ DLMM ДЛЯ binArraysCache ПАРАЛЛЕЛЬНО!
            console.log(`🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ 3 БИНА ИЗ DLMM ДЛЯ binArraysCache...`);

            // 🚀 ПАРАЛЛЕЛЬНАЯ ОБРАБОТКА ВСЕХ ПУЛОВ ОДНОВРЕМЕННО!
            const poolPromises = poolAddresses.map(async (poolAddress) => {
                const poolStr = this.getPoolStr(poolAddress);
                const shortPoolAddress = poolStr.slice(0, 8);
                const poolPubkey = new PublicKey(poolStr);

                try {
                    // 🔥 СНАЧАЛА СОЗДАЕМ ОБЫЧНЫЙ DLMM ДЛЯ ПОЛУЧЕНИЯ ДАННЫХ!
                    console.log(`🔍 ПОЛУЧАЕМ РЕАЛЬНЫЕ 3 БИНА ИЗ DLMM ДЛЯ binArraysCache...`);
                    const dlmmPool = await DLMM.create(connection, poolPubkey);

                    // 🔥 ПОЛУЧАЕМ ТОЛЬКО 3 БИНА ЧЕРЕЗ getBinsAroundActiveBin!
                    console.log(`🔥 ВЫЗЫВАЕМ getBinsAroundActiveBin(1, 1) для получения ТОЛЬКО 3 БИНОВ: АКТИВНЫЙ ± 1...`);

                    let threeBins = [];
                    let realActiveBin = dlmmPool.lbPair.activeId;

                    try {
                        const { activeBin, bins } = await dlmmPool.getBinsAroundActiveBin(1, 1);

                        if (!bins || bins.length === 0) {
                            throw new Error('Получен пустой массив бинов');
                        }

                        console.log(`✅ ПОЛУЧЕНО ${bins.length} РЕАЛЬНЫХ БИНОВ ИЗ API!`);

                        // 🔥 БЕРЕМ ТОЛЬКО 3 БИНА: АКТИВНЫЙ ± 1!
                        const activeBinIndex = bins.findIndex(bin => bin.binId === realActiveBin);
                        if (activeBinIndex === -1) {
                            throw new Error(`Активный бин ${realActiveBin} не найден в полученных бинах`);
                        }

                        // Берем 3 бина: [активный-1, активный, активный+1]
                        const threeBinsRaw = [
                            bins[activeBinIndex - 1] || bins[activeBinIndex], // Левый или активный
                            bins[activeBinIndex], // Активный
                            bins[activeBinIndex + 1] || bins[activeBinIndex]  // Правый или активный
                        ];

                        // 🔥 ПАРСИМ ТОЛЬКО 3 БИНА С РЕАЛЬНЫМИ ЦЕНАМИ И ЛИКВИДНОСТЬЮ!
                        threeBins = threeBinsRaw.map((bin, index) => ({
                            binId: bin.binId,
                            price: parseFloat(bin.pricePerToken || bin.price || 0),
                            pricePerToken: parseFloat(bin.pricePerToken || bin.price || 0),
                            isActive: bin.binId === realActiveBin,
                            // 🔥 ДОБАВЛЯЕМ РЕАЛЬНУЮ ЛИКВИДНОСТЬ ИЗ API!
                            xAmount: bin.xAmount || 0,           // WSOL amount (native)
                            yAmount: bin.yAmount || 0,           // USDC amount (native)
                            supply: bin.supply || 0,             // LP token supply
                            liquidityX: bin.xAmount || 0,        // WSOL liquidity (native)
                            liquidityY: bin.yAmount || 0         // USDC liquidity (native)
                        }));

                        console.log(`🔥 РЕАЛЬНЫЕ БИНЫ С ЦЕНАМИ:`);
                        threeBins.forEach(bin => {
                            console.log(`   Бин ${bin.binId}: цена ${bin.price} ${bin.isActive ? '(АКТИВНЫЙ)' : ''}`);
                        });

                    } catch (error) {
                        console.log(`❌ ОШИБКА getBinsAroundActiveBin: ${error.message}`);
                        console.log(`🔥 ПРОПУСКАЕМ ЭТОТ ПУЛ - НЕ МОЖЕМ ПОЛУЧИТЬ РЕАЛЬНЫЕ ЦЕНЫ!`);
                        return null; // Пропускаем пул если не можем получить реальные бины
                    }

                    const activeBinData = threeBins.find(bin => bin.binId === realActiveBin);
                    const activeBinPrice = activeBinData ? activeBinData.price : 0;

                    // 🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ BIN ARRAYS ЧЕРЕЗ API!
                    console.log(`🔥 ПОЛУЧАЕМ РЕАЛЬНЫЕ BIN ARRAYS ДЛЯ НАШИХ 3 БИНОВ...`);

                    let ourBinArrayAddresses = [];

                    try {
                        // 🚀 ПРОДАКШЕН: ПОЛУЧАЕМ BIN ARRAYS С КЕШИРОВАНИЕМ!
                        console.log(`🚀 ПОЛУЧАЕМ BIN ARRAYS С КЕШИРОВАНИЕМ (ПРОДАКШЕН ВЕРСИЯ)...`);

                        const activeBinId = realActiveBin;
                        console.log(`   Активный бин ID: ${activeBinId}`);

                        // 🎯 ПОЛУЧАЕМ CHUNK INDEXES ДЛЯ НАШИХ 3 БИНОВ!
                        const binIds = threeBins.map(bin => bin.binId);
                        console.log(`   Наши 3 бина: ${binIds.join(', ')}`);

                        // 🔥 ВЫЧИСЛЯЕМ CHUNK ДЛЯ КАЖДОГО ИЗ НАШИХ 3 БИНОВ!
                        const chunkIndexes = [...new Set(binIds.map(binId => Math.floor(binId / 64)))];
                        console.log(`   Уникальные chunk indexes для наших бинов: ${chunkIndexes.join(', ')}`);

                        if (chunkIndexes.length === 0) {
                            throw new Error('Не удалось вычислить chunk indexes для наших бинов');
                        }

                        console.log(`   Chunk indexes: ${chunkIndexes.join(', ')}`);

                        // 🎯 СОЗДАЕМ PDA ДЛЯ КАЖДОГО CHUNK
                        const binArrayPDAs = chunkIndexes.map(chunkIndex => {
                            console.log(`   Создаем PDA для chunk ${chunkIndex}...`);

                            // 🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЙ ФОРМАТ ДЛЯ METEORA DLMM (BigInt64LE)!
                            const seedChunk = Buffer.alloc(8); // 8 байт для BigInt64LE!
                            seedChunk.writeBigInt64LE(BigInt(chunkIndex), 0); // BigInt64LE работает!

                            const [pda] = PublicKey.findProgramAddressSync(
                                [
                                    Buffer.from('bin_array'),
                                    new PublicKey(poolStr).toBuffer(),
                                    seedChunk
                                ],
                                new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')
                            );

                            console.log(`   Chunk ${chunkIndex} → PDA: ${pda.toString().slice(0,8)}`);
                            return pda;
                        });

                        console.log(`   Проверяем PDA: ${binArrayPDAs.map(p => p.toString().slice(0,8)).join(', ')}`);

                        // 🔥 ПРОВЕРЯЕМ КАКИЕ BIN ARRAYS РЕАЛЬНО СУЩЕСТВУЮТ!
                        const accounts = await connection.getMultipleAccountsInfo(binArrayPDAs);
                        const existingBinArrays = [];

                        accounts.forEach((account, index) => {
                            const pda = binArrayPDAs[index];
                            const chunkIndex = chunkIndexes[index];
                            const exists = account !== null;

                            // 🔥 ПОКАЗЫВАЕМ КАКИЕ БИНЫ В ЭТОМ CHUNK'Е
                            const binsInChunk = binIds.filter(binId => Math.floor(binId / 64) === chunkIndex);

                            console.log(`   Chunk ${chunkIndex} (${pda.toString().slice(0,8)}): ${exists ? '✅ СУЩЕСТВУЕТ' : '❌ НЕ СУЩЕСТВУЕТ'} - бины: ${binsInChunk.join(', ')}`);

                            if (exists) {
                                existingBinArrays.push(pda);
                            } else {
                                console.log(`   ⚠️ CHUNK ${chunkIndex} НЕ СУЩЕСТВУЕТ! Бины ${binsInChunk.join(', ')} не смогут быть обработаны!`);
                            }
                        });

                        if (existingBinArrays.length > 0) {
                            ourBinArrayAddresses = existingBinArrays.map(pda => pda.toString());
                            console.log(`✅ НАЙДЕНО ${ourBinArrayAddresses.length} СУЩЕСТВУЮЩИХ BIN ARRAYS!`);
                            console.log(`   Bin Arrays: ${ourBinArrayAddresses.map(addr => addr.slice(0,8)).join(', ')}`);
                        } else {
                            throw new Error('НЕТ СУЩЕСТВУЮЩИХ BIN ARRAYS! Даже активный chunk не найден!');
                        }

                    } catch (error) {
                        console.log(`⚠️ Ошибка получения существующих bin arrays: ${error.message}`);
                        console.log(`🔄 ИСПОЛЬЗУЕМ СТАТИЧЕСКИЕ BIN ARRAYS ИЗ УСПЕШНЫХ ТРАНЗАКЦИЙ...`);

                        // 🔥 FALLBACK: РЕАЛЬНЫЕ BIN ARRAYS ИЗ УСПЕШНЫХ ТРАНЗАКЦИЙ METEORA!
                        if (poolStr === '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6') {
                            // Pool 1 - РЕАЛЬНЫЕ bin arrays из успешных транзакций
                            ourBinArrayAddresses = [
                                '15uxv5sbm16WmGyAwSaAGRMsSusCuH3fZkMmwLyqG8A', // Bin Array Lower из ошибки 3002
                                '18Bd8ZQYtbVUrKgYXGqA8xBMyi7eRswzPjVDgrX5sNN'  // Bin Array Upper из ошибки 3002
                            ];
                        } else if (poolStr === 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y') {
                            // Pool 2 - РЕАЛЬНЫЕ bin arrays (те же что для Pool 1, так как это один протокол)
                            ourBinArrayAddresses = [
                                '15uxv5sbm16WmGyAwSaAGRMsSusCuH3fZkMmwLyqG8A', // Bin Array Lower
                                '18Bd8ZQYtbVUrKgYXGqA8xBMyi7eRswzPjVDgrX5sNN'  // Bin Array Upper
                            ];
                        } else {
                            ourBinArrayAddresses = [];
                        }

                        console.log(`✅ ИСПОЛЬЗУЕМ СТАТИЧЕСКИЕ BIN ARRAYS: ${ourBinArrayAddresses.length} шт.`);
                    }

                    const binArraysCacheData = {
                        activeBinId: realActiveBin, // 🔥 ИСПОЛЬЗУЕМ РЕАЛЬНЫЙ АКТИВНЫЙ БИН!
                        activeBinPrice: activeBinPrice,
                        threeBins: threeBins,
                        binArrays: ourBinArrayAddresses, // 🔥 ДОБАВЛЯЕМ BIN ARRAYS!
                        timestamp: Date.now()
                    };

                    this.binArraysCache.set(poolStr, binArraysCacheData);

                    // 🔥 ДИАГНОСТИКА: ЧТО ИМЕННО СОХРАНИЛИ В КЭШ
                    console.log(`🔥 ДИАГНОСТИКА СОХРАНЕНИЯ В КЭШ ДЛЯ ${poolStr.slice(0,8)}:`);
                    console.log(`   activeBinId: ${binArraysCacheData.activeBinId}`);
                    console.log(`   threeBins: ${binArraysCacheData.threeBins.length} шт.`);
                    console.log(`   binArrays: ${binArraysCacheData.binArrays.length} шт.`);
                    console.log(`   timestamp: ${binArraysCacheData.timestamp}`);

                    // 🔥 СОХРАНЯЕМ DLMM INSTANCE В КЭШ ДЛЯ getDLMMInstance()!
                    this.dlmmInstancesCache.set(poolStr, dlmmPool);

                    console.log(`✅ РЕАЛЬНЫЕ 3 БИНА ПОЛУЧЕНЫ ДЛЯ ${shortPoolAddress}: ${threeBins.map(b => b.binId).join(', ')}`);
                    console.log(`✅ DLMM INSTANCE СОХРАНЕН В КЭШ ДЛЯ ${shortPoolAddress}`);

                    return { poolAddress: poolStr, success: true }; // Возвращаем успех

                } catch (error) {
                    console.log(`❌ Ошибка получения 3 бинов для ${poolStr.slice(0,8)}: ${error.message}`);
                    return null; // Возвращаем null для неудачных пулов
                }
            });

            // 🚀 ЖДЕМ ЗАВЕРШЕНИЯ ВСЕХ ПУЛОВ ПАРАЛЛЕЛЬНО!
            console.log(`🚀 ЗАПУСКАЕМ ПАРАЛЛЕЛЬНОЕ ОБНОВЛЕНИЕ ${poolAddresses.length} ПУЛОВ...`);
            const poolResults = await Promise.allSettled(poolPromises);

            let successCount = 0;
            poolResults.forEach((result, index) => {
                if (result.status === 'fulfilled' && result.value !== null) {
                    successCount++;
                } else {
                    console.log(`❌ Пул ${poolAddresses[index].slice(0,8)} не обновлен: ${result.reason || 'неизвестная ошибка'}`);
                }
            });

            console.log(`✅ ПАРАЛЛЕЛЬНОЕ ОБНОВЛЕНИЕ ЗАВЕРШЕНО: ${successCount}/${poolAddresses.length} пулов обновлено`);

            const results = [];
            // connection уже объявлен выше

            // 🔥 ОБРАБАТЫВАЕМ КАЖДЫЙ ПУЛ - ПОЛУЧАЕМ ЦЕНЫ ИЗ КЭША!
            for (const poolAddress of poolAddresses) {
                try {
                    const poolStr = this.getPoolStr(poolAddress);
                    const poolPubkey = new PublicKey(poolStr);

                    if (!this.arbitrageMode) {
                        console.log(`🔥 ИСПОЛЬЗУЕМ НАШИ 3 БИНА ИЗ КЭША: ${poolStr.slice(0, 8)}...`);
                    }

                    // 🔥 ИСПОЛЬЗУЕМ НАШИ РЕАЛЬНЫЕ БИНЫ ИЗ binArraysCache!
                    const cacheData = this.binArraysCache.get(poolStr);
                    if (!cacheData || !cacheData.threeBins || cacheData.threeBins.length === 0) {
                        console.log(`⚠️ НЕТ БИНОВ В КЭШЕ ДЛЯ ${poolStr.slice(0, 8)}! Пропускаем...`);
                        continue;
                    }

                    console.log(`✅ НАЙДЕНО ${cacheData.threeBins.length} БИНОВ В КЭШЕ ДЛЯ ${poolStr.slice(0, 8)}`);

                    // 🔥 ИСПОЛЬЗУЕМ НАШИ 3 БИНА ВМЕСТО SDK!
                    const activeBin = cacheData.threeBins[1].binId; // Средний бин
                    const bins = cacheData.threeBins; // Наши 3 бина

                    if (!bins || bins.length !== 3) {
                        console.log(`❌ НЕТ 3 БИНОВ ДЛЯ ${poolStr.slice(0, 8)}`);
                        continue;
                    }

                    // 🔥 ИСПОЛЬЗУЕМ ЦЕНУ ИЗ СРЕДНЕГО БИНА (АКТИВНЫЙ БИН)!
                    const activeBinData = bins[1]; // Средний бин из 3
                    let activePrice = activeBinData ? activeBinData.price : 0;

                    // 🔥 FALLBACK: ЕСЛИ ЦЕНА НУЛЕВАЯ, ПОЛУЧАЕМ ИЗ DLMM!
                    if (!activePrice || activePrice === 0) {
                        console.log(`⚠️ ЦЕНА НУЛЕВАЯ В КЭШЕ ДЛЯ ${poolStr.slice(0, 8)}, ПОЛУЧАЕМ ИЗ DLMM...`);
                        try {
                            const poolPubkey = new PublicKey(poolStr);
                            const connection = await this.getRPCForOperation('meteora');
                            const dlmmPool = await DLMM.create(connection, poolPubkey);

                            // 🔍 ДИАГНОСТИКА: ПРОВЕРЯЕМ ВСЕ ПОЛЯ DLMM!
                            console.log(`🔍 ДИАГНОСТИКА DLMM ДЛЯ ${poolStr.slice(0,8)}:`);
                            console.log(`   activeId: ${dlmmPool.lbPair.activeId}`);
                            console.log(`   activePrice: ${dlmmPool.lbPair.activePrice}`);
                            console.log(`   binStep: ${dlmmPool.lbPair.binStep}`);
                            console.log(`   baseFactor: ${dlmmPool.lbPair.baseFactor}`);

                            // 🔥 ВЫЧИСЛЯЕМ ЦЕНУ ИЗ activeId И binStep!
                            const activeId = dlmmPool.lbPair.activeId;
                            const binStep = dlmmPool.lbPair.binStep;

                            if (activeId !== undefined && binStep !== undefined) {
                                // Формула Meteora DLMM: price = (1 + binStep / 10000) ** activeId
                                activePrice = Math.pow(1 + binStep / 10000, activeId);
                                console.log(`🔥 ВЫЧИСЛЕНА ЦЕНА: (1 + ${binStep}/10000)^${activeId} = ${activePrice}`);
                            } else {
                                activePrice = 0;
                                console.log(`❌ НЕ УДАЛОСЬ ВЫЧИСЛИТЬ ЦЕНУ: activeId=${activeId}, binStep=${binStep}`);
                            }

                            console.log(`✅ ИТОГОВАЯ ЦЕНА: ${activePrice}`);
                        } catch (error) {
                            console.log(`❌ НЕ УДАЛОСЬ ПОЛУЧИТЬ ЦЕНУ ИЗ DLMM: ${error.message}`);
                            continue;
                        }
                    }

                    if (!activePrice || activePrice === 0) {
                        console.log(`❌ НЕТ ЦЕНЫ В СРЕДНЕМ БИНЕ ДЛЯ ${poolStr.slice(0, 8)}`);
                        continue;
                    }

                    if (!this.arbitrageMode) {
                        const age = Date.now() - Date.now(); // Возраст = 0 для свежих данных
                        const poolKey = poolStr === '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6' ? 'meteora1' : 'meteora2';
                        console.log(`✅ ${poolKey}: $${activePrice.toFixed(4)} (бин ID: ${activeBin}, возраст: ${age}ms)`);
                    }

                    // 🔥 ПОЛУЧАЕМ DLMM INSTANCE ИЗ КЭША!
                    const dlmmInstance = this.dlmmInstancesCache.get(poolStr);

                    // 🔥 СОХРАНЯЕМ ДАННЫЕ С DLMM INSTANCE!
                    const poolData = {
                        poolAddress: poolStr,
                        activeBinId: activeBin,
                        activeBinPrice: activePrice,
                        dlmmInstance: dlmmInstance, // 🔥 ИСПОЛЬЗУЕМ DLMM ИЗ КЭША!
                        bins: bins, // 🔥 НАШИ 3 БИНА ИЗ КЭША!
                        readyForSwap: true, // 🔥 ГОТОВЫ БЕЗ RPC ЗАПРОСОВ!
                        timestamp: cacheData.timestamp // 🔥 ВРЕМЯ ИЗ КЭША!
                    };

                    // 💾 КЭШИРУЕМ ДАННЫЕ ПУЛА В binArraysCache
                    // (данные уже сохранены в binArraysCache выше)
                    
                    results.push(poolData);

                } catch (poolError) {
                    const poolStr = this.getPoolStr(poolAddress);
                    console.log(`❌ Ошибка пула ${poolStr.slice(0, 8)}: ${poolError.message}`);
                }
            }

            const duration = Date.now() - startTime;
            
            if (!this.arbitrageMode) {
                console.log(`✅ Данные получены: ${results.length}/${poolAddresses.length} пулов за ${duration}ms`);
            }

            return results;

        } catch (error) {
            console.error(`❌ Ошибка получения данных:`, error.message);
            return [];
        }
    }

    /**
     * 🎯 СОЗДАНИЕ SWAP ТРАНЗАКЦИИ БЕЗ ЛИШНИХ ЗАПРОСОВ!
     * Использует кэшированный DLMM инстанс + getBinArrayForSwap() для создания транзакции
     */
    async createFastSwap(poolAddress, inAmount, swapForY = true, userPublicKey, minOutAmount = null) {
        const startTime = Date.now();
        
        try {
            const poolStr = this.getPoolStr(poolAddress);
            
            // 🎯 ПОЛУЧАЕМ DLMM ИНСТАНС ИЗ КЭША
            const dlmmInstance = this.dlmmInstancesCache.get(poolStr);
            
            if (!dlmmInstance) {
                throw new Error(`DLMM инстанс не найден в кэше для ${poolStr.slice(0, 8)}. Сначала вызовите getArbitrageData()`);
            }

            if (!this.arbitrageMode) {
                console.log(`🎯 СОЗДАНИЕ SWAP БЕЗ ДОПОЛНИТЕЛЬНЫХ RPC: ${poolStr.slice(0, 8)}`);
            }

            // 🔥 СОЗДАЕМ МГНОВЕННЫЙ QUOTE С НАШИМИ СВЕЖИМИ ДАННЫМИ!
            const quote = this.createFastQuoteFromCache(poolStr, inAmount, swapForY);

            const binArrays = quote.binArraysPubkey.map(pubkey => ({ publicKey: pubkey }));
            
            // 🔥 ЕСЛИ НЕ УКАЗАН minOutAmount - СТАВИМ МИНИМУМ (НАМ ПОХУЙ НА ТОЧНЫЙ ВЫХОД!)
            const finalMinOutAmount = minOutAmount || new BN(1);
            
            // 🎯 СОЗДАЕМ ТРАНЗАКЦИЮ НАПРЯМУЮ БЕЗ QUOTE!
            const poolPubkey = new PublicKey(poolStr);
            
            const swapTx = await dlmmInstance.swap({
                inToken: swapForY ? dlmmInstance.lbPair.tokenXMint : dlmmInstance.lbPair.tokenYMint,
                outToken: swapForY ? dlmmInstance.lbPair.tokenYMint : dlmmInstance.lbPair.tokenXMint,
                inAmount: inAmount,
                minOutAmount: finalMinOutAmount, // МИНИМУМ - НАМ ПОХУЙ!
                lbPair: poolPubkey,
                user: userPublicKey,
                binArraysPubkey: binArrays.map(ba => ba.publicKey)
            });

            const duration = Date.now() - startTime;
            
            if (!this.arbitrageMode) {
                console.log(`🎯 Swap транзакция создана за ${duration}ms БЕЗ ДОПОЛНИТЕЛЬНЫХ RPC!`);
                console.log(`   💰 Input: ${inAmount.toString()}`);
                console.log(`   🔗 Инструкций: ${swapTx.instructions.length}`);
            }

            return {
                transaction: swapTx,
                duration
            };

        } catch (error) {
            console.error(`❌ Ошибка создания swap транзакции:`, error.message);
            return null;
        }
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ ЦЕНЫ ИЗ КЭША
     */
    getPrice(poolAddress) {
        const poolData = this.binArraysCache.get(this.getPoolStr(poolAddress));
        return poolData ? poolData.activeBinPrice : null;
    }

    /**
     * 🎯 ПРОВЕРКА ГОТОВНОСТИ К SWAP
     */
    isReadyForSwap(poolAddress) {
        const poolData = this.binArraysCache.get(this.getPoolStr(poolAddress));
        return poolData && poolData.readyForSwap;
    }

    /**
     * 🎯 ВКЛЮЧЕНИЕ РЕЖИМА АРБИТРАЖА (скрывает логи)
     */
    startArbitrageMode(sessionId) {
        this.arbitrageMode = true;
        this.arbitrageSessionId = sessionId;
    }

    /**
     * 🎯 ВЫКЛЮЧЕНИЕ РЕЖИМА АРБИТРАЖА
     */
    endArbitrageMode() {
        this.arbitrageMode = false;
        this.arbitrageSessionId = null;
    }

    /**
     * 🎯 ОЧИСТКА УСТАРЕВШЕГО КЭША (ВЫЗЫВАЕТСЯ ВРУЧНУЮ) + ПРОДАКШЕН КЭШИ
     */
    cleanExpiredCache() {
        const now = Date.now();
        let cleaned = 0;

        // 🔥 ОЧИСТКА ОСНОВНОГО КЭША
        for (const [poolAddress, data] of this.binArraysCache.entries()) {
            if (now - data.timestamp > this.ACTIVE_BIN_CACHE_DURATION) {
                this.binArraysCache.delete(poolAddress);
                cleaned++;
            }
        }

        // 🚀 ПРОДАКШЕН: ОЧИСТКА PDA КЭША
        for (const [key, data] of this.pdaCache.entries()) {
            if (now - data.timestamp > this.pdaCacheTimeout) {
                this.pdaCache.delete(key);
                cleaned++;
            }
        }

        // 🚀 ПРОДАКШЕН: ОЧИСТКА КЭША СУЩЕСТВОВАНИЯ АККАУНТОВ
        for (const [key, data] of this.accountExistsCache.entries()) {
            if (now - data.timestamp > this.accountExistsCacheTimeout) {
                this.accountExistsCache.delete(key);
                cleaned++;
            }
        }

        if (cleaned > 0 && !this.arbitrageMode) {
            console.log(`🧹 Очищено ${cleaned} устаревших записей из всех кэшей (основной + PDA + существование)`);
        }
    }

    /**
     * 🎯 МЕТОДЫ СОВМЕСТИМОСТИ СО СТАРЫМ КОДОМ
     */

    // Совместимость с batchUpdateAllActiveBins
    async batchUpdateAllActiveBins(poolAddresses) {
        return await this.getArbitrageData(poolAddresses);
    }

    // ❌ УДАЛЕНО: getActiveBinFromCache() - СОЗДАВАЛА ФЕЙКИ И ПЕРЕЗАПИСЫВАЛА РЕАЛЬНЫЕ ДАННЫЕ!

    // 🔥 ПРАВИЛЬНЫЙ getAllExactPrices ИЗ binArraysCache!
    getAllExactPrices() {
        const prices = new Map();

        for (const [poolAddress, poolData] of this.binArraysCache.entries()) {
            const age = Date.now() - poolData.timestamp;
            prices.set(poolAddress, {
                price: poolData.activeBinPrice,
                binId: poolData.activeBinId,
                age: age,
                fresh: true,
                timestamp: poolData.timestamp
            });
        }

        return prices;
    }

    // 🚫 СОВМЕСТИМОСТЬ: getDLMMInstance - возвращает кэшированный DLMM
    async getDLMMInstance(poolAddress) {
        if (!poolAddress) {
            throw new Error(`🚨 КРИТИЧЕСКАЯ ОШИБКА: poolAddress не передан в getDLMMInstance! Получен: ${poolAddress}`);
        }

        const poolStr = this.getPoolStr(poolAddress);
        const dlmmInstance = this.dlmmInstancesCache.get(poolStr);

        if (!dlmmInstance) {
            console.log(`⚠️ DLMM инстанс не найден в кэше для ${poolStr.slice(0, 8)}. Вызовите getArbitrageData() сначала.`);
            return null;
        }

        return dlmmInstance;
    }

    // 🚫 СОВМЕСТИМОСТЬ: updateActiveBin - обновляет данные активного бина
    async updateActiveBin(poolAddress) {
        try {
            const poolStr = this.getPoolStr(poolAddress);

            if (!this.arbitrageMode) {
                console.log(`🔥 ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ КЭША ДЛЯ ${poolStr.slice(0, 8)}...`);
            }

            // Получаем свежие данные через getArbitrageData
            const arbitrageData = await this.getArbitrageData([poolAddress]);

            if (arbitrageData && arbitrageData.length > 0) {
                if (!this.arbitrageMode) {
                    console.log(`✅ Кэш обновлен для ${poolStr.slice(0, 8)}: цена=$${arbitrageData[0].activeBinPrice.toFixed(6)}`);
                }
                return true;
            } else {
                console.log(`❌ Не удалось обновить кэш для ${poolStr.slice(0, 8)}`);
                return false;
            }

        } catch (error) {
            console.log(`⚠️ ОШИБКА ОБНОВЛЕНИЯ КЭША: ${error.message}`);
            return false;
        }
    }

    // 🚫 СОВМЕСТИМОСТЬ: getActiveBinData - получает данные активного бина
    getActiveBinData(poolAddress) {
        const poolStr = this.getPoolStr(poolAddress);
        const poolData = this.binArraysCache.get(poolStr);

        if (!poolData) {
            console.log(`🔍 ОТЛАДКА КЭША: Ищем ${poolAddress}`);
            console.log(`🔍 ОТЛАДКА КЭША: Ищем ${poolStr.slice(0, 8)}`);
            console.log(`❌ НЕТ КЭША АКТИВНЫХ БИНОВ ДЛЯ ${poolStr.slice(0, 8)}!`);
            return null;
        }

        // Возвращаем данные в формате, ожидаемом старым кодом
        return {
            activeBinId: poolData.activeBinId,
            activeBinPrice: poolData.activeBinPrice,
            bins: poolData.threeBins, // 🔥 ПРАВИЛЬНОЕ ПОЛЕ!
            binArrays: poolData.binArrays, // 🔥 ДОБАВЛЯЕМ BIN ARRAYS!
            timestamp: poolData.timestamp
        };
    }

    // 🔥 СОЗДАНИЕ МГНОВЕННОГО DLMM С НАШИМИ СВЕЖИМИ ДАННЫМИ!
    async createFastDLMM(connection, poolPubkey) {
        const poolStr = poolPubkey.toString();
        console.log(`🚀 СОЗДАЕМ МГНОВЕННЫЙ DLMM С НАШИМИ ДАННЫМИ: ${poolStr.slice(0,8)}...`);

        // 🔥 ПОЛУЧАЕМ НАШИ СВЕЖИЕ ДАННЫЕ ИЗ ПРАВИЛЬНОГО КЭША!
        const cacheData = this.binArraysCache.get(poolStr);
        if (!cacheData) {
            throw new Error(`❌ НЕТ СВЕЖИХ ДАННЫХ В binArraysCache ДЛЯ ${poolStr.slice(0,8)}!`);
        }

        // 🔥 СОЗДАЕМ DLMM С НАШИМИ СВЕЖИМИ ДАННЫМИ БЕЗ RPC!
        const provider = new (require('@coral-xyz/anchor')).AnchorProvider(
            connection,
            {},
            require('@coral-xyz/anchor').AnchorProvider.defaultOptions()
        );

        const program = new (require('@coral-xyz/anchor')).Program(
            require('@meteora-ag/dlmm').IDL,
            new PublicKey("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"),
            provider
        );

        // 🔥 СОЗДАЕМ FAKE LB PAIR С НАШИМИ СВЕЖИМИ ДАННЫМИ!
        const fakeLbPair = {
            activeId: cacheData.activeBinId,
            binStep: 25, // Стандартный bin step
            tokenXMint: new PublicKey("So11111111111111111111111111111111111111112"), // SOL
            tokenYMint: new PublicKey("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"), // USDC
            reserveX: new PublicKey("11111111111111111111111111111111"),
            reserveY: new PublicKey("11111111111111111111111111111111"),
            rewardInfos: [
                { mint: new PublicKey("11111111111111111111111111111111"), vault: new PublicKey("11111111111111111111111111111111") },
                { mint: new PublicKey("11111111111111111111111111111111"), vault: new PublicKey("11111111111111111111111111111111") }
            ]
        };

        const fakeTokenReserve = {
            publicKey: new PublicKey("11111111111111111111111111111111"),
            mint: { decimals: 9 },
            owner: new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"),
            transferHookAccountMetas: []
        };

        const fakeClock = {
            slot: new BN(0),
            unixTimestamp: new BN(Date.now() / 1000),
            epoch: new BN(0)
        };

        // 🔥 СОЗДАЕМ МГНОВЕННЫЙ DLMM!
        const fastDLMM = new DLMM(
            poolPubkey,
            program,
            fakeLbPair,
            null, // binArrayBitmapExtension
            fakeTokenReserve, // tokenX
            fakeTokenReserve, // tokenY
            [null, null], // rewards
            fakeClock
        );

        console.log(`✅ МГНОВЕННЫЙ DLMM СОЗДАН С НАШИМИ ДАННЫМИ: activeBinId=${cacheData.activeBinId}, price=$${cacheData.activeBinPrice.toFixed(4)}`);
        return fastDLMM;
    }

    // 🔥 СОЗДАНИЕ МГНОВЕННОГО QUOTE С НАШИМИ СВЕЖИМИ ДАННЫМИ!
    createFastQuoteFromCache(poolStr, inAmount, swapForY) {
        console.log(`⚡ СОЗДАЕМ МГНОВЕННЫЙ QUOTE С НАШИМИ ДАННЫМИ...`);

        // 🔥 ПОЛУЧАЕМ НАШИ СВЕЖИЕ ДАННЫЕ ИЗ binArraysCache!
        const cacheData = this.binArraysCache.get(poolStr);
        if (!cacheData || !cacheData.threeBins) {
            throw new Error(`❌ НЕТ СВЕЖИХ 3 БИНОВ В binArraysCache ДЛЯ ${poolStr.slice(0,8)}!`);
        }

        // 🔥 РАССЧИТЫВАЕМ ПРИМЕРНЫЙ ВЫХОД ИЗ НАШИХ 3 БИНОВ!
        const activeBinPrice = cacheData.activeBinPrice;
        let estimatedOut;

        if (swapForY) {
            // SOL -> USDC: умножаем на цену
            estimatedOut = new BN(Math.floor(inAmount.toNumber() * activeBinPrice * 0.997)); // -0.3% комиссия
        } else {
            // USDC -> SOL: делим на цену
            estimatedOut = new BN(Math.floor(inAmount.toNumber() / activeBinPrice * 0.997)); // -0.3% комиссия
        }

        // 🔥 ИСПОЛЬЗУЕМ РЕАЛЬНЫЕ BIN ARRAYS ИЗ КЭША!
        if (!cacheData.binArrays || cacheData.binArrays.length === 0) {
            throw new Error(`❌ НЕТ BIN ARRAYS В КЭШЕ ДЛЯ ${poolStr.slice(0,8)}!`);
        }

        const realBinArrays = cacheData.binArrays.map(addr => new PublicKey(addr));
        console.log(`🔥 ИСПОЛЬЗУЕМ ${realBinArrays.length} РЕАЛЬНЫХ BIN ARRAYS ИЗ КЭША!`);

        // 🔥 МГНОВЕННЫЙ QUOTE БЕЗ RPC ЗАПРОСОВ!
        const fastQuote = {
            inAmount: inAmount,
            outAmount: estimatedOut,
            minOutAmount: estimatedOut.mul(new BN(99)).div(new BN(100)), // -1% slippage
            binArraysPubkey: realBinArrays,
            priceImpact: 0.1, // 0.1% примерно
            fee: inAmount.mul(new BN(3)).div(new BN(1000)) // 0.3% комиссия
        };

        console.log(`⚡ МГНОВЕННЫЙ QUOTE СОЗДАН: ${inAmount.toString()} -> ${estimatedOut.toString()}`);
        return fastQuote;
    }
}

module.exports = MeteoraBinCacheManager;
