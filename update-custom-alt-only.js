/**
 * Обновление ТОЛЬКО custom таблицы в custom-alt-data.json
 * НЕ ТРОГАЕМ marginfi таблицы!
 */

const fs = require('fs');

async function updateCustomALTOnly() {
    console.log('🔥 ОБНОВЛЕНИЕ ТОЛЬКО CUSTOM ALT ТАБЛИЦЫ');
    console.log('=' .repeat(60));

    try {
        // 1. Загружаем текущий файл custom-alt-data.json
        console.log('📁 Загружаем текущий custom-alt-data.json...');
        const currentData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
        console.log(`✅ Текущие данные загружены: ${currentData.totalTables} таблиц`);

        // 2. Загружаем обновленные данные custom таблицы
        console.log('📁 Загружаем обновленные данные custom-alt-data-updated.json...');
        const updatedData = JSON.parse(fs.readFileSync('custom-alt-data-updated.json', 'utf8'));
        console.log(`✅ Обновленные данные загружены: ${updatedData.addresses.length} адресов`);

        // 3. Проверяем наши новые BIN ARRAYS
        const newBinArrays = [
            'CnwbYbAjLGiDiGNNDVp2jjHBm41gL2t9xfjU5iCvDtET',
            '3PtxzAh6LxUteSy97M9thBkZQHfzWbRLSi8G6oJjfynd'
        ];

        console.log('\n🔍 ПРОВЕРКА НОВЫХ BIN ARRAYS В ОБНОВЛЕННЫХ ДАННЫХ:');
        newBinArrays.forEach((addr, index) => {
            const found = updatedData.addresses.includes(addr);
            console.log(`   ${index + 1}. ${addr}: ${found ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'}`);
        });

        // 4. Обновляем ТОЛЬКО custom таблицу
        console.log('\n🔄 ОБНОВЛЯЕМ ТОЛЬКО CUSTOM ТАБЛИЦУ...');
        
        // Сохраняем marginfi таблицы как есть
        const marginfiCount = currentData.tables.marginfi1.accountCount;
        console.log(`📊 MarginFi таблица: ${marginfiCount} адресов (НЕ ТРОГАЕМ)`);

        // Обновляем custom таблицу
        currentData.tables.custom = {
            address: updatedData.address,
            accountCount: updatedData.addresses.length,
            addresses: updatedData.addresses,
            valid: true
        };

        // Обновляем общую статистику
        currentData.totalAccounts = marginfiCount + updatedData.addresses.length;
        currentData.timestamp = new Date().toISOString();
        currentData.source = "🔥 CUSTOM ТАБЛИЦА ОБНОВЛЕНА С НОВЫМИ BIN ARRAYS";

        console.log(`✅ Custom таблица обновлена: ${updatedData.addresses.length} адресов`);
        console.log(`📊 Общий счет: ${currentData.totalAccounts} адресов`);

        // 5. Создаем резервную копию
        const backupFile = `custom-alt-data.json.backup.${Date.now()}`;
        fs.writeFileSync(backupFile, fs.readFileSync('custom-alt-data.json', 'utf8'));
        console.log(`💾 Резервная копия: ${backupFile}`);

        // 6. Сохраняем обновленный файл
        fs.writeFileSync('custom-alt-data.json', JSON.stringify(currentData, null, 4));
        console.log('💾 Файл custom-alt-data.json обновлен!');

        // 7. Проверяем результат
        console.log('\n🔍 ПРОВЕРКА РЕЗУЛЬТАТА:');
        const verifyData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
        console.log(`📊 Всего таблиц: ${verifyData.totalTables}`);
        console.log(`📊 Всего адресов: ${verifyData.totalAccounts}`);
        console.log(`📊 MarginFi адресов: ${verifyData.tables.marginfi1.accountCount}`);
        console.log(`📊 Custom адресов: ${verifyData.tables.custom.accountCount}`);

        // Проверяем наши BIN ARRAYS в финальном файле
        console.log('\n🔍 ФИНАЛЬНАЯ ПРОВЕРКА НОВЫХ BIN ARRAYS:');
        newBinArrays.forEach((addr, index) => {
            const found = verifyData.tables.custom.addresses.includes(addr);
            console.log(`   ${index + 1}. ${addr}: ${found ? '✅ НАЙДЕН' : '❌ НЕ НАЙДЕН'}`);
        });

        console.log('\n🎉 ОБНОВЛЕНИЕ ЗАВЕРШЕНО!');
        console.log('✅ MarginFi таблицы НЕ ТРОНУТЫ');
        console.log('✅ Custom таблица обновлена с новыми BIN ARRAYS');
        console.log('✅ Система готова к использованию обновленных ALT таблиц');

    } catch (error) {
        console.error('❌ Ошибка обновления:', error.message);
        process.exit(1);
    }
}

updateCustomALTOnly();
